import React, { useRef, useEffect } from 'react';

interface Star {
    x: number;
    y: number;
    vx: number;
    vy: number;
    size: number;
    opacity: number;
    pulseSpeed: number;
    pulsePhase: number;
}

const ConstellationBackground: React.FC = () => {
    const canvasRef = useRef<HTMLCanvasElement>(null);

    useEffect(() => {
        const canvas = canvasRef.current;
        if (!canvas) return;

        const ctx = canvas.getContext('2d');
        if (!ctx) return;

        let animationId: number;
        let stars: Star[] = [];
        const maxDistance = 200; // Distance maximale pour connecter les étoiles (augmentée)
        const starCount = 120; // Nombre d'étoiles (augmenté)
        let mouseX = 0;
        let mouseY = 0;
        let mouseInfluence = 180; // Rayon d'influence de la souris (augmenté)
        let mouseConnections = true; // Activer les connexions vers la souris

        // Couleurs du gradient Gemini
        const geminiColors = [
            '#1A3452', // Bleu Gemini foncé
            '#2190F6', // Bleu Gemini
            '#6689EF', // Violet intermédiaire
            '#8D86ED', // Violet Gemini
            '#AE87F3'  // Violet clair Gemini
        ];

        const initialize = () => {
            canvas.width = window.innerWidth;
            canvas.height = window.innerHeight;

            stars = [];
            for (let i = 0; i < starCount; i++) {
                stars.push({
                    x: Math.random() * canvas.width,
                    y: Math.random() * canvas.height,
                    vx: (Math.random() - 0.5) * 0.5, // Vitesse très lente
                    vy: (Math.random() - 0.5) * 0.5,
                    size: Math.random() * 3 + 1,
                    opacity: Math.random() * 0.8 + 0.2,
                    pulseSpeed: Math.random() * 0.02 + 0.01,
                    pulsePhase: Math.random() * Math.PI * 2
                });
            }
        };

        const drawStar = (star: Star, time: number) => {
            // Effet de pulsation
            const pulse = Math.sin(time * star.pulseSpeed + star.pulsePhase) * 0.3 + 0.7;
            const currentOpacity = star.opacity * pulse;
            
            const colorIndex = Math.floor((star.x + star.y) / (canvas.width + canvas.height) * geminiColors.length);
            const color = geminiColors[colorIndex % geminiColors.length];

            ctx.save();
            ctx.globalAlpha = currentOpacity;

            // Gradient radial pour l'étoile
            const gradient = ctx.createRadialGradient(star.x, star.y, 0, star.x, star.y, star.size * 3);
            
            gradient.addColorStop(0, color);
            gradient.addColorStop(0.5, color);
            gradient.addColorStop(1, color);

            ctx.fillStyle = gradient;
            ctx.beginPath();
            ctx.arc(star.x, star.y, star.size, 0, Math.PI * 2);
            ctx.fill();

            // Effet de lueur supplémentaire pour certaines étoiles
            if (star.size > 2.5) {
                ctx.shadowColor = color;
                ctx.shadowBlur = 10;
                ctx.fillStyle = color;
                ctx.beginPath();
                ctx.arc(star.x, star.y, star.size * 0.5, 0, Math.PI * 2);
                ctx.fill();
                ctx.shadowBlur = 0;
            }
            ctx.restore();
        };

        const drawConnection = (star1: Star, star2: Star, distance: number) => {
            const opacity = Math.max(0, (maxDistance - distance) / maxDistance) * 0.3;
            
            const color1Index = Math.floor((star1.x + star1.y) / (canvas.width + canvas.height) * geminiColors.length);
            const color2Index = Math.floor((star2.x + star2.y) / (canvas.width + canvas.height) * geminiColors.length);
            const color1 = geminiColors[color1Index % geminiColors.length];
            const color2 = geminiColors[color2Index % geminiColors.length];

            ctx.save();
            ctx.globalAlpha = opacity;

            // Gradient linéaire pour la connexion
            const gradient = ctx.createLinearGradient(star1.x, star1.y, star2.x, star2.y);
            
            gradient.addColorStop(0, color1);
            gradient.addColorStop(1, color2);

            ctx.strokeStyle = gradient;
            ctx.lineWidth = 1;
            ctx.beginPath();
            ctx.moveTo(star1.x, star1.y);
            ctx.lineTo(star2.x, star2.y);
            ctx.stroke();
            ctx.restore();
        };

        const drawMouseConnections = () => {
            // Dessiner les connexions entre la souris et les étoiles proches
            if (mouseX >= 0 && mouseY >= 0) {
                stars.forEach(star => {
                    const dx = mouseX - star.x;
                    const dy = mouseY - star.y;
                    const distance = Math.sqrt(dx * dx + dy * dy);

                    if (distance < mouseInfluence) {
                        const opacity = Math.max(0, (mouseInfluence - distance) / mouseInfluence) * 0.6;

                        ctx.save();
                        ctx.globalAlpha = opacity;

                        // Gradient vers la souris
                        const gradient = ctx.createLinearGradient(star.x, star.y, mouseX, mouseY);
                        const colorIndex = Math.floor((star.x + star.y) / (canvas.width + canvas.height) * geminiColors.length);
                        const color = geminiColors[colorIndex % geminiColors.length];

                        gradient.addColorStop(0, color);
                        gradient.addColorStop(1, '#AE87F3'); // Couleur Gemini claire pour la souris

                        ctx.strokeStyle = gradient;
                        ctx.lineWidth = 2;
                        ctx.beginPath();
                        ctx.moveTo(star.x, star.y);
                        ctx.lineTo(mouseX, mouseY);
                        ctx.stroke();
                        ctx.restore();
                    }
                });

                // Dessiner un point lumineux à la position de la souris
                ctx.save();
                ctx.globalAlpha = 0.8;
                const mouseGradient = ctx.createRadialGradient(mouseX, mouseY, 0, mouseX, mouseY, 15);
                mouseGradient.addColorStop(0, '#AE87F3');
                mouseGradient.addColorStop(0.5, '#8D86ED');
                mouseGradient.addColorStop(1, 'rgba(174, 135, 243, 0)');

                ctx.fillStyle = mouseGradient;
                ctx.beginPath();
                ctx.arc(mouseX, mouseY, 15, 0, Math.PI * 2);
                ctx.fill();
                ctx.restore();
            }
        };

        const animate = (time: number) => {
            // Fond noir complet pour éviter l'effet d'effacement
            ctx.fillStyle = '#000000';
            ctx.fillRect(0, 0, canvas.width, canvas.height);

            // Mettre à jour les positions des étoiles avec influence de la souris
            stars.forEach(star => {
                // Influence de la souris plus forte
                const dx = mouseX - star.x;
                const dy = mouseY - star.y;
                const distance = Math.sqrt(dx * dx + dy * dy);

                if (distance < mouseInfluence && distance > 0) {
                    const force = (mouseInfluence - distance) / mouseInfluence;
                    star.vx += (dx / distance) * force * 0.03; // Force augmentée
                    star.vy += (dy / distance) * force * 0.03;
                }

                star.x += star.vx;
                star.y += star.vy;

                // Friction pour ralentir progressivement
                star.vx *= 0.98; // Friction légèrement réduite pour plus de mouvement
                star.vy *= 0.98;

                // Rebond sur les bords
                if (star.x < 0 || star.x > canvas.width) star.vx *= -1;
                if (star.y < 0 || star.y > canvas.height) star.vy *= -1;

                // Garder les étoiles dans les limites
                star.x = Math.max(0, Math.min(canvas.width, star.x));
                star.y = Math.max(0, Math.min(canvas.height, star.y));
            });

            // Dessiner les connexions entre étoiles
            for (let i = 0; i < stars.length; i++) {
                for (let j = i + 1; j < stars.length; j++) {
                    const dx = stars[i].x - stars[j].x;
                    const dy = stars[i].y - stars[j].y;
                    const distance = Math.sqrt(dx * dx + dy * dy);

                    if (distance < maxDistance) {
                        drawConnection(stars[i], stars[j], distance);
                    }
                }
            }

            // Dessiner les connexions vers la souris
            if (mouseConnections) {
                drawMouseConnections();
            }

            // Dessiner les étoiles
            stars.forEach(star => drawStar(star, time));

            animationId = requestAnimationFrame(animate);
        };

        initialize();
        animate(0);

        const handleResize = () => {
            initialize();
        };

        const handleMouseMove = (event: MouseEvent) => {
            const rect = canvas.getBoundingClientRect();
            mouseX = event.clientX - rect.left;
            mouseY = event.clientY - rect.top;
        };

        const handleMouseLeave = () => {
            mouseX = -1; // Valeur négative pour indiquer que la souris n'est pas sur le canvas
            mouseY = -1;
        };

        const handleMouseEnter = (event: MouseEvent) => {
            const rect = canvas.getBoundingClientRect();
            mouseX = event.clientX - rect.left;
            mouseY = event.clientY - rect.top;
        };

        window.addEventListener('resize', handleResize);
        canvas.addEventListener('mousemove', handleMouseMove);
        canvas.addEventListener('mouseleave', handleMouseLeave);
        canvas.addEventListener('mouseenter', handleMouseEnter);

        // Ajouter aussi des listeners sur le document pour une meilleure capture
        document.addEventListener('mousemove', handleMouseMove);

        return () => {
            cancelAnimationFrame(animationId);
            window.removeEventListener('resize', handleResize);
            canvas.removeEventListener('mousemove', handleMouseMove);
            canvas.removeEventListener('mouseleave', handleMouseLeave);
            canvas.removeEventListener('mouseenter', handleMouseEnter);
            document.removeEventListener('mousemove', handleMouseMove);
        };
    }, []);

    return (
        <div className="fixed top-0 left-0 w-full h-full -z-10" style={{ background: '#000' }}>
            <canvas
                ref={canvasRef}
                className="absolute top-0 left-0 w-full h-full"
                style={{
                    pointerEvents: 'auto',
                    zIndex: 1
                }}
            />
        </div>
    );
};

export default ConstellationBackground;
