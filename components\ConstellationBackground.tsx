import React, { useRef, useEffect } from 'react';

interface Star {
    x: number;
    y: number;
    vx: number;
    vy: number;
    size: number;
    opacity: number;
    pulseSpeed: number;
    pulsePhase: number;
    colorIndex: number;
}

const ConstellationBackground: React.FC = () => {
    const canvasRef = useRef<HTMLCanvasElement>(null);

    useEffect(() => {
        const canvas = canvasRef.current;
        if (!canvas) return;

        const ctx = canvas.getContext('2d');
        if (!ctx) return;

        let animationId: number;
        let stars: Star[] = [];
        const maxDistance = 160; // Distance maximale pour connecter les étoiles
        const starCount = 280; // Encore plus d'étoiles pour un ciel très dense
        let mouseX = 0;
        let mouseY = 0;
        let mouseInfluence = 140; // Rayon d'influence de la souris
        let mouseConnections = true; // Activer les connexions vers la souris

        // Variables pour la rotation céleste - ENCORE PLUS LENTE
        let celestialRotationSpeed = 0.00005; // Rotation ultra-lente pour plus de réalisme
        let centerX = 0;
        let centerY = 0;

        // Couleurs réalistes des étoiles basées sur leur température
        const starColors = [
            '#FFFFFF', // Blanc pur - étoiles très chaudes (majorité)
            '#F8F8FF', // Blanc fantôme - étoiles chaudes
            '#E6E6FA', // Lavande très pâle - étoiles blanches
            '#87CEEB', // Bleu ciel - étoiles très chaudes (type O/B)
            '#B0E0E6', // Bleu poudre - étoiles chaudes
            '#FFE4B5', // Blanc cassé/crème - étoiles comme le Soleil
            '#FFEFD5', // Blanc papaye - étoiles légèrement plus froides
            '#FFB347', // Orange pêche - étoiles plus froides (type K)
            '#FF6347', // Rouge tomate - étoiles froides (type M)
            '#CD853F'  // Brun doré - étoiles très froides
        ];

        const initialize = () => {
            canvas.width = window.innerWidth;
            canvas.height = window.innerHeight;

            // Centre de rotation (légèrement décalé pour un effet plus naturel)
            centerX = canvas.width * 0.6; // Décalé vers la droite
            centerY = canvas.height * 0.3; // Décalé vers le haut

            stars = [];
            for (let i = 0; i < starCount; i++) {
                const x = Math.random() * canvas.width;
                const y = Math.random() * canvas.height;

                // Distribution réaliste des étoiles par taille et couleur
                const starType = Math.random();
                let size, opacity, pulseSpeed, colorIndex;

                if (starType < 0.6) {
                    // Petites étoiles blanches (60% - majorité)
                    size = Math.random() * 1.2 + 0.3;
                    opacity = Math.random() * 0.5 + 0.4;
                    pulseSpeed = Math.random() * 0.003 + 0.001; // Scintillement très subtil
                    colorIndex = Math.floor(Math.random() * 3); // Blanc, blanc fantôme, lavande
                } else if (starType < 0.8) {
                    // Étoiles moyennes (20% - mélange de couleurs)
                    size = Math.random() * 1.8 + 1.2;
                    opacity = Math.random() * 0.6 + 0.5;
                    pulseSpeed = Math.random() * 0.005 + 0.002;
                    colorIndex = Math.floor(Math.random() * 6); // Blanc à orange pêche
                } else if (starType < 0.95) {
                    // Étoiles brillantes (15% - couleurs variées)
                    size = Math.random() * 2.2 + 1.8;
                    opacity = Math.random() * 0.8 + 0.6;
                    pulseSpeed = Math.random() * 0.007 + 0.003;
                    colorIndex = Math.floor(Math.random() * starColors.length);
                } else {
                    // Étoiles géantes très brillantes (5% - toutes couleurs)
                    size = Math.random() * 3 + 2.5;
                    opacity = Math.random() * 0.9 + 0.7;
                    pulseSpeed = Math.random() * 0.008 + 0.004;
                    colorIndex = Math.floor(Math.random() * starColors.length);
                }

                stars.push({
                    x: x,
                    y: y,
                    vx: (Math.random() - 0.5) * 0.08, // Vitesse encore plus réduite
                    vy: (Math.random() - 0.5) * 0.08,
                    size: size,
                    opacity: opacity,
                    pulseSpeed: pulseSpeed,
                    pulsePhase: Math.random() * Math.PI * 2,
                    colorIndex: colorIndex // Ajouter l'index de couleur à chaque étoile
                });
            }
        };

        // Fonction pour appliquer la rotation céleste
        const applyCelestialRotation = (star: Star, time: number) => {
            // Calculer la position relative au centre de rotation
            const dx = star.x - centerX;
            const dy = star.y - centerY;

            // Calculer l'angle de rotation basé sur le temps
            const rotationAngle = time * celestialRotationSpeed;

            // Appliquer la rotation
            const cos = Math.cos(rotationAngle);
            const sin = Math.sin(rotationAngle);

            const rotatedX = dx * cos - dy * sin;
            const rotatedY = dx * sin + dy * cos;

            // Retourner les nouvelles coordonnées
            return {
                x: rotatedX + centerX,
                y: rotatedY + centerY
            };
        };

        const drawStar = (star: Star, time: number) => {
            // Appliquer la rotation céleste
            const rotatedPos = applyCelestialRotation(star, time);

            // Scintillement naturel très subtil (comme les vraies étoiles)
            const twinkle1 = Math.sin(time * star.pulseSpeed + star.pulsePhase) * 0.15 + 0.85;
            const twinkle2 = Math.sin(time * star.pulseSpeed * 1.3 + star.pulsePhase * 0.7) * 0.1 + 0.9;
            const currentOpacity = star.opacity * twinkle1 * twinkle2;

            // Utiliser la couleur assignée à l'étoile
            const color = starColors[star.colorIndex] || starColors[0];

            ctx.save();
            ctx.globalAlpha = currentOpacity;

            // Gradient radial pour l'étoile avec effet de halo naturel
            const gradient = ctx.createRadialGradient(rotatedPos.x, rotatedPos.y, 0, rotatedPos.x, rotatedPos.y, star.size * 2.5);

            gradient.addColorStop(0, color);
            gradient.addColorStop(0.3, color);
            gradient.addColorStop(0.7, color + '80'); // Semi-transparent
            gradient.addColorStop(1, color + '00'); // Transparent

            ctx.fillStyle = gradient;
            ctx.beginPath();
            ctx.arc(rotatedPos.x, rotatedPos.y, star.size, 0, Math.PI * 2);
            ctx.fill();

            // Effet de lueur douce pour les grandes étoiles
            if (star.size > 2) {
                ctx.globalAlpha = currentOpacity * 0.3;
                ctx.shadowColor = color;
                ctx.shadowBlur = star.size * 2;
                ctx.fillStyle = color;
                ctx.beginPath();
                ctx.arc(rotatedPos.x, rotatedPos.y, star.size * 0.3, 0, Math.PI * 2);
                ctx.fill();
                ctx.shadowBlur = 0;
            }
            ctx.restore();

            // Retourner la position rotée pour les connexions
            return rotatedPos;
        };

        const drawConnection = (star1: Star, star2: Star, pos1: {x: number, y: number}, pos2: {x: number, y: number}, distance: number) => {
            const opacity = Math.max(0, (maxDistance - distance) / maxDistance) * 0.08; // Connexions très subtiles

            // Utiliser les couleurs des étoiles connectées
            const color1 = starColors[star1.colorIndex] || starColors[0];
            const color2 = starColors[star2.colorIndex] || starColors[0];

            ctx.save();
            ctx.globalAlpha = opacity;

            // Gradient linéaire pour la connexion
            const gradient = ctx.createLinearGradient(pos1.x, pos1.y, pos2.x, pos2.y);

            gradient.addColorStop(0, color1);
            gradient.addColorStop(1, color2);

            ctx.strokeStyle = gradient;
            ctx.lineWidth = 0.8; // Lignes plus fines
            ctx.beginPath();
            ctx.moveTo(pos1.x, pos1.y);
            ctx.lineTo(pos2.x, pos2.y);
            ctx.stroke();
            ctx.restore();
        };

        const drawMouseConnections = (time: number) => {
            // Dessiner les connexions entre la souris et les étoiles proches
            if (mouseX >= 0 && mouseY >= 0) {
                stars.forEach(star => {
                    const rotatedPos = applyCelestialRotation(star, time);
                    const dx = mouseX - rotatedPos.x;
                    const dy = mouseY - rotatedPos.y;
                    const distance = Math.sqrt(dx * dx + dy * dy);

                    if (distance < mouseInfluence) {
                        const opacity = Math.max(0, (mouseInfluence - distance) / mouseInfluence) * 0.6;

                        ctx.save();
                        ctx.globalAlpha = opacity;

                        // Gradient vers la souris
                        const gradient = ctx.createLinearGradient(rotatedPos.x, rotatedPos.y, mouseX, mouseY);
                        const color = starColors[star.colorIndex] || starColors[0];

                        gradient.addColorStop(0, color);
                        gradient.addColorStop(1, '#FFFFFF'); // Blanc pour la souris (plus naturel)

                        ctx.strokeStyle = gradient;
                        ctx.lineWidth = 2;
                        ctx.beginPath();
                        ctx.moveTo(rotatedPos.x, rotatedPos.y);
                        ctx.lineTo(mouseX, mouseY);
                        ctx.stroke();
                        ctx.restore();
                    }
                });

                // Dessiner un point lumineux subtil à la position de la souris
                ctx.save();
                ctx.globalAlpha = 0.4;
                const mouseGradient = ctx.createRadialGradient(mouseX, mouseY, 0, mouseX, mouseY, 12);
                mouseGradient.addColorStop(0, '#FFFFFF');
                mouseGradient.addColorStop(0.3, '#F8F8FF');
                mouseGradient.addColorStop(1, 'rgba(255, 255, 255, 0)');

                ctx.fillStyle = mouseGradient;
                ctx.beginPath();
                ctx.arc(mouseX, mouseY, 12, 0, Math.PI * 2);
                ctx.fill();
                ctx.restore();
            }
        };

        const animate = (time: number) => {
            // Fond noir complet pour éviter l'effet d'effacement
            ctx.fillStyle = '#000000';
            ctx.fillRect(0, 0, canvas.width, canvas.height);

            // Mettre à jour les positions des étoiles avec influence de la souris (sur les positions de base)
            stars.forEach(star => {
                // Calculer la position actuelle avec rotation céleste pour l'influence de la souris
                const rotatedPos = applyCelestialRotation(star, time);
                const dx = mouseX - rotatedPos.x;
                const dy = mouseY - rotatedPos.y;
                const distance = Math.sqrt(dx * dx + dy * dy);

                if (distance < mouseInfluence && distance > 0) {
                    const force = (mouseInfluence - distance) / mouseInfluence;
                    // Appliquer la force sur les positions de base (pas les positions rotées)
                    star.vx += (dx / distance) * force * 0.005; // Force très douce pour préserver la rotation naturelle
                    star.vy += (dy / distance) * force * 0.005;
                }

                star.x += star.vx;
                star.y += star.vy;

                // Friction pour ralentir progressivement
                star.vx *= 0.99; // Friction plus forte pour maintenir la rotation céleste
                star.vy *= 0.99;

                // Rebond sur les bords (sur les positions de base)
                if (star.x < 0 || star.x > canvas.width) star.vx *= -1;
                if (star.y < 0 || star.y > canvas.height) star.vy *= -1;

                // Garder les étoiles dans les limites
                star.x = Math.max(0, Math.min(canvas.width, star.x));
                star.y = Math.max(0, Math.min(canvas.height, star.y));
            });

            // Calculer les positions rotées pour les connexions
            const rotatedPositions = stars.map(star => applyCelestialRotation(star, time));

            // Dessiner les connexions entre étoiles (avec positions rotées)
            for (let i = 0; i < stars.length; i++) {
                for (let j = i + 1; j < stars.length; j++) {
                    const pos1 = rotatedPositions[i];
                    const pos2 = rotatedPositions[j];
                    const dx = pos1.x - pos2.x;
                    const dy = pos1.y - pos2.y;
                    const distance = Math.sqrt(dx * dx + dy * dy);

                    if (distance < maxDistance) {
                        drawConnection(stars[i], stars[j], pos1, pos2, distance);
                    }
                }
            }

            // Dessiner les connexions vers la souris
            if (mouseConnections) {
                drawMouseConnections(time);
            }

            // Dessiner les étoiles (avec rotation céleste)
            stars.forEach(star => drawStar(star, time));

            animationId = requestAnimationFrame(animate);
        };

        initialize();
        animate(0);

        const handleResize = () => {
            initialize();
        };

        const handleMouseMove = (event: MouseEvent) => {
            const rect = canvas.getBoundingClientRect();
            mouseX = event.clientX - rect.left;
            mouseY = event.clientY - rect.top;
        };

        const handleMouseLeave = () => {
            mouseX = -1; // Valeur négative pour indiquer que la souris n'est pas sur le canvas
            mouseY = -1;
        };

        const handleMouseEnter = (event: MouseEvent) => {
            const rect = canvas.getBoundingClientRect();
            mouseX = event.clientX - rect.left;
            mouseY = event.clientY - rect.top;
        };

        window.addEventListener('resize', handleResize);
        canvas.addEventListener('mousemove', handleMouseMove);
        canvas.addEventListener('mouseleave', handleMouseLeave);
        canvas.addEventListener('mouseenter', handleMouseEnter);

        // Ajouter aussi des listeners sur le document pour une meilleure capture
        document.addEventListener('mousemove', handleMouseMove);

        return () => {
            cancelAnimationFrame(animationId);
            window.removeEventListener('resize', handleResize);
            canvas.removeEventListener('mousemove', handleMouseMove);
            canvas.removeEventListener('mouseleave', handleMouseLeave);
            canvas.removeEventListener('mouseenter', handleMouseEnter);
            document.removeEventListener('mousemove', handleMouseMove);
        };
    }, []);

    return (
        <div className="fixed top-0 left-0 w-full h-full -z-10" style={{ background: '#000' }}>
            <canvas
                ref={canvasRef}
                className="absolute top-0 left-0 w-full h-full"
                style={{
                    pointerEvents: 'auto',
                    zIndex: 1
                }}
            />
        </div>
    );
};

export default ConstellationBackground;
