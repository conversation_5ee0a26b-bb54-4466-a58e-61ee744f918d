import React, { useRef, useEffect } from 'react';

interface Star {
    x: number;
    y: number;
    vx: number;
    vy: number;
    size: number;
    opacity: number;
    pulseSpeed: number;
    pulsePhase: number;
    colorIndex: number;
    depth: number;
}

const ConstellationBackground: React.FC = () => {
    const canvasRef = useRef<HTMLCanvasElement>(null);

    useEffect(() => {
        const canvas = canvasRef.current;
        if (!canvas) return;

        const ctx = canvas.getContext('2d');
        if (!ctx) return;

        let animationId: number;
        let stars: Star[] = [];
        const maxDistance = 150; // Distance maximale pour connecter les étoiles
        const starCount = 450; // Beaucoup plus d'étoiles pour un vrai ciel étoilé
        let mouseX = 0;
        let mouseY = 0;
        let mouseInfluence = 130; // Rayon d'influence de la souris
        let mouseConnections = true; // Activer les connexions vers la souris

        // Variables pour la rotation céleste - ENCORE PLUS LENTE
        let celestialRotationSpeed = 0.00005; // Rotation ultra-lente pour plus de réalisme
        let centerX = 0;
        let centerY = 0;

        // Couleurs réalistes des étoiles basées sur leur température
        const starColors = [
            '#FFFFFF', // Blanc pur - étoiles très chaudes (majorité)
            '#F8F8FF', // Blanc fantôme - étoiles chaudes
            '#E6E6FA', // Lavande très pâle - étoiles blanches
            '#87CEEB', // Bleu ciel - étoiles très chaudes (type O/B)
            '#B0E0E6', // Bleu poudre - étoiles chaudes
            '#FFE4B5', // Blanc cassé/crème - étoiles comme le Soleil
            '#FFEFD5', // Blanc papaye - étoiles légèrement plus froides
            '#FFB347', // Orange pêche - étoiles plus froides (type K)
            '#FF6347', // Rouge tomate - étoiles froides (type M)
            '#CD853F'  // Brun doré - étoiles très froides
        ];

        const initialize = () => {
            canvas.width = window.innerWidth;
            canvas.height = window.innerHeight;

            // Centre de rotation (légèrement décalé pour un effet plus naturel)
            centerX = canvas.width * 0.6; // Décalé vers la droite
            centerY = canvas.height * 0.3; // Décalé vers le haut

            stars = [];

            // Créer des couches d'étoiles pour l'effet de profondeur
            for (let i = 0; i < starCount; i++) {
                // Distribution plus large sur tout l'écran avec zones de densité variable
                let x, y;
                const zoneType = Math.random();

                if (zoneType < 0.7) {
                    // 70% des étoiles réparties uniformément
                    x = Math.random() * canvas.width;
                    y = Math.random() * canvas.height;
                } else {
                    // 30% des étoiles dans des zones plus denses (simulation de la Voie Lactée)
                    x = Math.random() * canvas.width;
                    y = Math.random() * canvas.height * 0.6 + canvas.height * 0.2; // Zone centrale
                }

                // Couches de profondeur pour créer l'illusion de distance
                const depthLayer = Math.random();
                let size, opacity, pulseSpeed, colorIndex, depth;

                if (depthLayer < 0.4) {
                    // Couche d'arrière-plan - étoiles très lointaines (40%)
                    size = Math.random() * 0.8 + 0.2; // Très petites
                    opacity = Math.random() * 0.3 + 0.1; // Très faibles
                    pulseSpeed = Math.random() * 0.002 + 0.0005; // Scintillement minimal
                    colorIndex = Math.floor(Math.random() * 3); // Principalement blanc
                    depth = 0.3; // Profondeur lointaine
                } else if (depthLayer < 0.7) {
                    // Couche intermédiaire - étoiles moyennes (30%)
                    size = Math.random() * 1.5 + 0.5;
                    opacity = Math.random() * 0.5 + 0.3;
                    pulseSpeed = Math.random() * 0.004 + 0.001;
                    colorIndex = Math.floor(Math.random() * 6); // Couleurs variées
                    depth = 0.6; // Profondeur moyenne
                } else if (depthLayer < 0.9) {
                    // Couche avant - étoiles proches (20%)
                    size = Math.random() * 2.5 + 1.0;
                    opacity = Math.random() * 0.7 + 0.4;
                    pulseSpeed = Math.random() * 0.006 + 0.002;
                    colorIndex = Math.floor(Math.random() * starColors.length);
                    depth = 0.8; // Profondeur proche
                } else {
                    // Étoiles très brillantes au premier plan (10%)
                    size = Math.random() * 3.5 + 2.0;
                    opacity = Math.random() * 0.9 + 0.6;
                    pulseSpeed = Math.random() * 0.008 + 0.003;
                    colorIndex = Math.floor(Math.random() * starColors.length);
                    depth = 1.0; // Premier plan
                }

                stars.push({
                    x: x,
                    y: y,
                    vx: (Math.random() - 0.5) * 0.06 * depth, // Vitesse proportionnelle à la profondeur
                    vy: (Math.random() - 0.5) * 0.06 * depth,
                    size: size,
                    opacity: opacity,
                    pulseSpeed: pulseSpeed,
                    pulsePhase: Math.random() * Math.PI * 2,
                    colorIndex: colorIndex,
                    depth: depth // Ajouter la profondeur pour les effets
                });
            }
        };

        // Fonction pour appliquer la rotation céleste
        const applyCelestialRotation = (star: Star, time: number) => {
            // Calculer la position relative au centre de rotation
            const dx = star.x - centerX;
            const dy = star.y - centerY;

            // Calculer l'angle de rotation basé sur le temps
            const rotationAngle = time * celestialRotationSpeed;

            // Appliquer la rotation
            const cos = Math.cos(rotationAngle);
            const sin = Math.sin(rotationAngle);

            const rotatedX = dx * cos - dy * sin;
            const rotatedY = dx * sin + dy * cos;

            // Retourner les nouvelles coordonnées
            return {
                x: rotatedX + centerX,
                y: rotatedY + centerY
            };
        };

        const drawStar = (star: Star, time: number) => {
            // Appliquer la rotation céleste
            const rotatedPos = applyCelestialRotation(star, time);

            // Scintillement naturel multi-couches (comme les vraies étoiles)
            const twinkle1 = Math.sin(time * star.pulseSpeed + star.pulsePhase) * 0.12 + 0.88;
            const twinkle2 = Math.sin(time * star.pulseSpeed * 1.7 + star.pulsePhase * 0.3) * 0.08 + 0.92;
            const twinkle3 = Math.sin(time * star.pulseSpeed * 0.6 + star.pulsePhase * 1.4) * 0.05 + 0.95;
            const currentOpacity = star.opacity * twinkle1 * twinkle2 * twinkle3 * star.depth;

            // Utiliser la couleur assignée à l'étoile
            const color = starColors[star.colorIndex] || starColors[0];

            ctx.save();

            // COUCHE 1: Halo externe très diffus (atmosphère)
            if (star.size > 0.8) {
                ctx.globalAlpha = currentOpacity * 0.15 * star.depth;
                ctx.shadowColor = color;
                ctx.shadowBlur = star.size * 8 * star.depth; // Halo proportionnel à la profondeur

                const haloGradient = ctx.createRadialGradient(
                    rotatedPos.x, rotatedPos.y, 0,
                    rotatedPos.x, rotatedPos.y, star.size * 6 * star.depth
                );
                haloGradient.addColorStop(0, color + '40'); // Très transparent
                haloGradient.addColorStop(0.5, color + '20');
                haloGradient.addColorStop(1, color + '00');

                ctx.fillStyle = haloGradient;
                ctx.beginPath();
                ctx.arc(rotatedPos.x, rotatedPos.y, star.size * 6 * star.depth, 0, Math.PI * 2);
                ctx.fill();
                ctx.shadowBlur = 0;
            }

            // COUCHE 2: Halo moyen (lueur principale)
            ctx.globalAlpha = currentOpacity * 0.4;
            const mediumHaloGradient = ctx.createRadialGradient(
                rotatedPos.x, rotatedPos.y, 0,
                rotatedPos.x, rotatedPos.y, star.size * 3.5
            );
            mediumHaloGradient.addColorStop(0, color + '80');
            mediumHaloGradient.addColorStop(0.4, color + '40');
            mediumHaloGradient.addColorStop(1, color + '00');

            ctx.fillStyle = mediumHaloGradient;
            ctx.beginPath();
            ctx.arc(rotatedPos.x, rotatedPos.y, star.size * 3.5, 0, Math.PI * 2);
            ctx.fill();

            // COUCHE 3: Corps de l'étoile avec blur subtil
            ctx.globalAlpha = currentOpacity;
            ctx.shadowColor = color;
            ctx.shadowBlur = star.size * 1.5; // Blur léger pour simuler l'atmosphère

            const coreGradient = ctx.createRadialGradient(
                rotatedPos.x, rotatedPos.y, 0,
                rotatedPos.x, rotatedPos.y, star.size * 1.2
            );
            coreGradient.addColorStop(0, color);
            coreGradient.addColorStop(0.6, color + 'CC');
            coreGradient.addColorStop(1, color + '80');

            ctx.fillStyle = coreGradient;
            ctx.beginPath();
            ctx.arc(rotatedPos.x, rotatedPos.y, star.size, 0, Math.PI * 2);
            ctx.fill();

            // COUCHE 4: Point central brillant pour les grandes étoiles
            if (star.size > 1.5) {
                ctx.globalAlpha = currentOpacity * 0.9;
                ctx.shadowBlur = star.size * 0.5;
                ctx.fillStyle = color;
                ctx.beginPath();
                ctx.arc(rotatedPos.x, rotatedPos.y, star.size * 0.3, 0, Math.PI * 2);
                ctx.fill();
            }

            ctx.shadowBlur = 0;
            ctx.restore();

            // Retourner la position rotée pour les connexions
            return rotatedPos;
        };

        const drawConnection = (star1: Star, star2: Star, pos1: {x: number, y: number}, pos2: {x: number, y: number}, distance: number) => {
            const opacity = Math.max(0, (maxDistance - distance) / maxDistance) * 0.08; // Connexions très subtiles

            // Utiliser les couleurs des étoiles connectées
            const color1 = starColors[star1.colorIndex] || starColors[0];
            const color2 = starColors[star2.colorIndex] || starColors[0];

            ctx.save();
            ctx.globalAlpha = opacity;

            // Gradient linéaire pour la connexion
            const gradient = ctx.createLinearGradient(pos1.x, pos1.y, pos2.x, pos2.y);

            gradient.addColorStop(0, color1);
            gradient.addColorStop(1, color2);

            ctx.strokeStyle = gradient;
            ctx.lineWidth = 0.8; // Lignes plus fines
            ctx.beginPath();
            ctx.moveTo(pos1.x, pos1.y);
            ctx.lineTo(pos2.x, pos2.y);
            ctx.stroke();
            ctx.restore();
        };

        const drawMouseConnections = (time: number) => {
            // Dessiner les connexions entre la souris et les étoiles proches
            if (mouseX >= 0 && mouseY >= 0) {
                stars.forEach(star => {
                    const rotatedPos = applyCelestialRotation(star, time);
                    const dx = mouseX - rotatedPos.x;
                    const dy = mouseY - rotatedPos.y;
                    const distance = Math.sqrt(dx * dx + dy * dy);

                    if (distance < mouseInfluence) {
                        const opacity = Math.max(0, (mouseInfluence - distance) / mouseInfluence) * 0.6;

                        ctx.save();
                        ctx.globalAlpha = opacity;

                        // Gradient vers la souris
                        const gradient = ctx.createLinearGradient(rotatedPos.x, rotatedPos.y, mouseX, mouseY);
                        const color = starColors[star.colorIndex] || starColors[0];

                        gradient.addColorStop(0, color);
                        gradient.addColorStop(1, '#FFFFFF'); // Blanc pour la souris (plus naturel)

                        ctx.strokeStyle = gradient;
                        ctx.lineWidth = 2;
                        ctx.beginPath();
                        ctx.moveTo(rotatedPos.x, rotatedPos.y);
                        ctx.lineTo(mouseX, mouseY);
                        ctx.stroke();
                        ctx.restore();
                    }
                });

                // Dessiner un point lumineux subtil à la position de la souris
                ctx.save();
                ctx.globalAlpha = 0.4;
                const mouseGradient = ctx.createRadialGradient(mouseX, mouseY, 0, mouseX, mouseY, 12);
                mouseGradient.addColorStop(0, '#FFFFFF');
                mouseGradient.addColorStop(0.3, '#F8F8FF');
                mouseGradient.addColorStop(1, 'rgba(255, 255, 255, 0)');

                ctx.fillStyle = mouseGradient;
                ctx.beginPath();
                ctx.arc(mouseX, mouseY, 12, 0, Math.PI * 2);
                ctx.fill();
                ctx.restore();
            }
        };

        const animate = (time: number) => {
            // Fond noir complet pour éviter l'effet d'effacement
            ctx.fillStyle = '#000000';
            ctx.fillRect(0, 0, canvas.width, canvas.height);

            // Mettre à jour les positions des étoiles avec influence de la souris (sur les positions de base)
            stars.forEach(star => {
                // Calculer la position actuelle avec rotation céleste pour l'influence de la souris
                const rotatedPos = applyCelestialRotation(star, time);
                const dx = mouseX - rotatedPos.x;
                const dy = mouseY - rotatedPos.y;
                const distance = Math.sqrt(dx * dx + dy * dy);

                if (distance < mouseInfluence && distance > 0) {
                    const force = (mouseInfluence - distance) / mouseInfluence;
                    // Appliquer la force sur les positions de base (pas les positions rotées)
                    star.vx += (dx / distance) * force * 0.005; // Force très douce pour préserver la rotation naturelle
                    star.vy += (dy / distance) * force * 0.005;
                }

                star.x += star.vx;
                star.y += star.vy;

                // Friction pour ralentir progressivement
                star.vx *= 0.99; // Friction plus forte pour maintenir la rotation céleste
                star.vy *= 0.99;

                // Rebond sur les bords (sur les positions de base)
                if (star.x < 0 || star.x > canvas.width) star.vx *= -1;
                if (star.y < 0 || star.y > canvas.height) star.vy *= -1;

                // Garder les étoiles dans les limites
                star.x = Math.max(0, Math.min(canvas.width, star.x));
                star.y = Math.max(0, Math.min(canvas.height, star.y));
            });

            // Calculer les positions rotées pour les connexions
            const rotatedPositions = stars.map(star => applyCelestialRotation(star, time));

            // Dessiner les connexions entre étoiles (avec positions rotées)
            for (let i = 0; i < stars.length; i++) {
                for (let j = i + 1; j < stars.length; j++) {
                    const pos1 = rotatedPositions[i];
                    const pos2 = rotatedPositions[j];
                    const dx = pos1.x - pos2.x;
                    const dy = pos1.y - pos2.y;
                    const distance = Math.sqrt(dx * dx + dy * dy);

                    if (distance < maxDistance) {
                        drawConnection(stars[i], stars[j], pos1, pos2, distance);
                    }
                }
            }

            // Dessiner les connexions vers la souris
            if (mouseConnections) {
                drawMouseConnections(time);
            }

            // Dessiner les étoiles (avec rotation céleste)
            stars.forEach(star => drawStar(star, time));

            animationId = requestAnimationFrame(animate);
        };

        initialize();
        animate(0);

        const handleResize = () => {
            initialize();
        };

        const handleMouseMove = (event: MouseEvent) => {
            const rect = canvas.getBoundingClientRect();
            mouseX = event.clientX - rect.left;
            mouseY = event.clientY - rect.top;
        };

        const handleMouseLeave = () => {
            mouseX = -1; // Valeur négative pour indiquer que la souris n'est pas sur le canvas
            mouseY = -1;
        };

        const handleMouseEnter = (event: MouseEvent) => {
            const rect = canvas.getBoundingClientRect();
            mouseX = event.clientX - rect.left;
            mouseY = event.clientY - rect.top;
        };

        window.addEventListener('resize', handleResize);
        canvas.addEventListener('mousemove', handleMouseMove);
        canvas.addEventListener('mouseleave', handleMouseLeave);
        canvas.addEventListener('mouseenter', handleMouseEnter);

        // Ajouter aussi des listeners sur le document pour une meilleure capture
        document.addEventListener('mousemove', handleMouseMove);

        return () => {
            cancelAnimationFrame(animationId);
            window.removeEventListener('resize', handleResize);
            canvas.removeEventListener('mousemove', handleMouseMove);
            canvas.removeEventListener('mouseleave', handleMouseLeave);
            canvas.removeEventListener('mouseenter', handleMouseEnter);
            document.removeEventListener('mousemove', handleMouseMove);
        };
    }, []);

    return (
        <div className="fixed top-0 left-0 w-full h-full -z-10" style={{ background: '#000' }}>
            <canvas
                ref={canvasRef}
                className="absolute top-0 left-0 w-full h-full"
                style={{
                    pointerEvents: 'auto',
                    zIndex: 1
                }}
            />
        </div>
    );
};

export default ConstellationBackground;
