import React, { useRef, useEffect } from 'react';

interface Star {
    x: number;
    y: number;
    vx: number;
    vy: number;
    size: number;
    opacity: number;
    pulseSpeed: number;
    pulsePhase: number;
}

const ConstellationBackground: React.FC = () => {
    const canvasRef = useRef<HTMLCanvasElement>(null);

    useEffect(() => {
        const canvas = canvasRef.current;
        if (!canvas) return;

        const ctx = canvas.getContext('2d');
        if (!ctx) return;

        let animationId: number;
        let stars: Star[] = [];
        const maxDistance = 200; // Distance maximale pour connecter les étoiles (augmentée)
        const starCount = 120; // Nombre d'étoiles (augmenté)
        let mouseX = 0;
        let mouseY = 0;
        let mouseInfluence = 180; // Rayon d'influence de la souris (augmenté)
        let mouseConnections = true; // Activer les connexions vers la souris

        // Variables pour la rotation céleste
        let celestialRotationSpeed = 0.0008; // Vitesse de rotation plus visible pour le test
        let centerX = 0;
        let centerY = 0;

        // Couleurs du gradient Gemini
        const geminiColors = [
            '#1A3452', // Bleu Gemini foncé
            '#2190F6', // Bleu Gemini
            '#6689EF', // Violet intermédiaire
            '#8D86ED', // Violet Gemini
            '#AE87F3'  // Violet clair Gemini
        ];

        const initialize = () => {
            canvas.width = window.innerWidth;
            canvas.height = window.innerHeight;

            // Centre de rotation (légèrement décalé pour un effet plus naturel)
            centerX = canvas.width * 0.6; // Décalé vers la droite
            centerY = canvas.height * 0.3; // Décalé vers le haut

            stars = [];
            for (let i = 0; i < starCount; i++) {
                const x = Math.random() * canvas.width;
                const y = Math.random() * canvas.height;

                stars.push({
                    x: x,
                    y: y,
                    vx: (Math.random() - 0.5) * 0.2, // Vitesse réduite pour la rotation céleste
                    vy: (Math.random() - 0.5) * 0.2,
                    size: Math.random() * 3 + 1,
                    opacity: Math.random() * 0.8 + 0.2,
                    pulseSpeed: Math.random() * 0.02 + 0.01,
                    pulsePhase: Math.random() * Math.PI * 2
                });
            }
        };

        // Fonction pour appliquer la rotation céleste
        const applyCelestialRotation = (star: Star, time: number) => {
            // Calculer la position relative au centre de rotation
            const dx = star.x - centerX;
            const dy = star.y - centerY;

            // Calculer l'angle de rotation basé sur le temps
            const rotationAngle = time * celestialRotationSpeed;

            // Appliquer la rotation
            const cos = Math.cos(rotationAngle);
            const sin = Math.sin(rotationAngle);

            const rotatedX = dx * cos - dy * sin;
            const rotatedY = dx * sin + dy * cos;

            // Retourner les nouvelles coordonnées
            return {
                x: rotatedX + centerX,
                y: rotatedY + centerY
            };
        };

        const drawStar = (star: Star, time: number) => {
            // Appliquer la rotation céleste
            const rotatedPos = applyCelestialRotation(star, time);

            // Effet de pulsation
            const pulse = Math.sin(time * star.pulseSpeed + star.pulsePhase) * 0.3 + 0.7;
            const currentOpacity = star.opacity * pulse;

            // Sécuriser le calcul de couleur
            const colorIndex = Math.floor(Math.abs(rotatedPos.x + rotatedPos.y) / (canvas.width + canvas.height) * geminiColors.length);
            const color = geminiColors[Math.min(colorIndex, geminiColors.length - 1)] || geminiColors[0];

            ctx.save();
            ctx.globalAlpha = currentOpacity;

            // Gradient radial pour l'étoile
            const gradient = ctx.createRadialGradient(rotatedPos.x, rotatedPos.y, 0, rotatedPos.x, rotatedPos.y, star.size * 3);

            gradient.addColorStop(0, color);
            gradient.addColorStop(0.5, color);
            gradient.addColorStop(1, color);

            ctx.fillStyle = gradient;
            ctx.beginPath();
            ctx.arc(rotatedPos.x, rotatedPos.y, star.size, 0, Math.PI * 2);
            ctx.fill();

            // Effet de lueur supplémentaire pour certaines étoiles
            if (star.size > 2.5) {
                ctx.shadowColor = color;
                ctx.shadowBlur = 10;
                ctx.fillStyle = color;
                ctx.beginPath();
                ctx.arc(rotatedPos.x, rotatedPos.y, star.size * 0.5, 0, Math.PI * 2);
                ctx.fill();
                ctx.shadowBlur = 0;
            }
            ctx.restore();

            // Retourner la position rotée pour les connexions
            return rotatedPos;
        };

        const drawConnection = (pos1: {x: number, y: number}, pos2: {x: number, y: number}, distance: number) => {
            const opacity = Math.max(0, (maxDistance - distance) / maxDistance) * 0.3;

            // Sécuriser les calculs d'index et s'assurer qu'on a des couleurs valides
            const color1Index = Math.floor(Math.abs(pos1.x + pos1.y) / (canvas.width + canvas.height) * geminiColors.length);
            const color2Index = Math.floor(Math.abs(pos2.x + pos2.y) / (canvas.width + canvas.height) * geminiColors.length);
            const color1 = geminiColors[Math.min(color1Index, geminiColors.length - 1)] || geminiColors[0];
            const color2 = geminiColors[Math.min(color2Index, geminiColors.length - 1)] || geminiColors[0];

            ctx.save();
            ctx.globalAlpha = opacity;

            // Gradient linéaire pour la connexion
            const gradient = ctx.createLinearGradient(pos1.x, pos1.y, pos2.x, pos2.y);

            gradient.addColorStop(0, color1);
            gradient.addColorStop(1, color2);

            ctx.strokeStyle = gradient;
            ctx.lineWidth = 1;
            ctx.beginPath();
            ctx.moveTo(pos1.x, pos1.y);
            ctx.lineTo(pos2.x, pos2.y);
            ctx.stroke();
            ctx.restore();
        };

        const drawMouseConnections = (time: number) => {
            // Dessiner les connexions entre la souris et les étoiles proches
            if (mouseX >= 0 && mouseY >= 0) {
                stars.forEach(star => {
                    const rotatedPos = applyCelestialRotation(star, time);
                    const dx = mouseX - rotatedPos.x;
                    const dy = mouseY - rotatedPos.y;
                    const distance = Math.sqrt(dx * dx + dy * dy);

                    if (distance < mouseInfluence) {
                        const opacity = Math.max(0, (mouseInfluence - distance) / mouseInfluence) * 0.6;

                        ctx.save();
                        ctx.globalAlpha = opacity;

                        // Gradient vers la souris
                        const gradient = ctx.createLinearGradient(rotatedPos.x, rotatedPos.y, mouseX, mouseY);
                        const colorIndex = Math.floor(Math.abs(rotatedPos.x + rotatedPos.y) / (canvas.width + canvas.height) * geminiColors.length);
                        const color = geminiColors[Math.min(colorIndex, geminiColors.length - 1)] || geminiColors[0];

                        gradient.addColorStop(0, color);
                        gradient.addColorStop(1, '#AE87F3'); // Couleur Gemini claire pour la souris

                        ctx.strokeStyle = gradient;
                        ctx.lineWidth = 2;
                        ctx.beginPath();
                        ctx.moveTo(rotatedPos.x, rotatedPos.y);
                        ctx.lineTo(mouseX, mouseY);
                        ctx.stroke();
                        ctx.restore();
                    }
                });

                // Dessiner un point lumineux à la position de la souris
                ctx.save();
                ctx.globalAlpha = 0.8;
                const mouseGradient = ctx.createRadialGradient(mouseX, mouseY, 0, mouseX, mouseY, 15);
                mouseGradient.addColorStop(0, '#AE87F3');
                mouseGradient.addColorStop(0.5, '#8D86ED');
                mouseGradient.addColorStop(1, 'rgba(174, 135, 243, 0)');

                ctx.fillStyle = mouseGradient;
                ctx.beginPath();
                ctx.arc(mouseX, mouseY, 15, 0, Math.PI * 2);
                ctx.fill();
                ctx.restore();
            }
        };

        const animate = (time: number) => {
            // Fond noir complet pour éviter l'effet d'effacement
            ctx.fillStyle = '#000000';
            ctx.fillRect(0, 0, canvas.width, canvas.height);

            // Mettre à jour les positions des étoiles avec influence de la souris (sur les positions de base)
            stars.forEach(star => {
                // Calculer la position actuelle avec rotation céleste pour l'influence de la souris
                const rotatedPos = applyCelestialRotation(star, time);
                const dx = mouseX - rotatedPos.x;
                const dy = mouseY - rotatedPos.y;
                const distance = Math.sqrt(dx * dx + dy * dy);

                if (distance < mouseInfluence && distance > 0) {
                    const force = (mouseInfluence - distance) / mouseInfluence;
                    // Appliquer la force sur les positions de base (pas les positions rotées)
                    star.vx += (dx / distance) * force * 0.01; // Force réduite pour ne pas perturber la rotation
                    star.vy += (dy / distance) * force * 0.01;
                }

                star.x += star.vx;
                star.y += star.vy;

                // Friction pour ralentir progressivement
                star.vx *= 0.99; // Friction plus forte pour maintenir la rotation céleste
                star.vy *= 0.99;

                // Rebond sur les bords (sur les positions de base)
                if (star.x < 0 || star.x > canvas.width) star.vx *= -1;
                if (star.y < 0 || star.y > canvas.height) star.vy *= -1;

                // Garder les étoiles dans les limites
                star.x = Math.max(0, Math.min(canvas.width, star.x));
                star.y = Math.max(0, Math.min(canvas.height, star.y));
            });

            // Calculer les positions rotées pour les connexions
            const rotatedPositions = stars.map(star => applyCelestialRotation(star, time));

            // Dessiner les connexions entre étoiles (avec positions rotées)
            for (let i = 0; i < stars.length; i++) {
                for (let j = i + 1; j < stars.length; j++) {
                    const pos1 = rotatedPositions[i];
                    const pos2 = rotatedPositions[j];
                    const dx = pos1.x - pos2.x;
                    const dy = pos1.y - pos2.y;
                    const distance = Math.sqrt(dx * dx + dy * dy);

                    if (distance < maxDistance) {
                        drawConnection(pos1, pos2, distance);
                    }
                }
            }

            // Dessiner les connexions vers la souris
            if (mouseConnections) {
                drawMouseConnections(time);
            }

            // Dessiner les étoiles (avec rotation céleste)
            stars.forEach(star => drawStar(star, time));

            animationId = requestAnimationFrame(animate);
        };

        initialize();
        animate(0);

        const handleResize = () => {
            initialize();
        };

        const handleMouseMove = (event: MouseEvent) => {
            const rect = canvas.getBoundingClientRect();
            mouseX = event.clientX - rect.left;
            mouseY = event.clientY - rect.top;
        };

        const handleMouseLeave = () => {
            mouseX = -1; // Valeur négative pour indiquer que la souris n'est pas sur le canvas
            mouseY = -1;
        };

        const handleMouseEnter = (event: MouseEvent) => {
            const rect = canvas.getBoundingClientRect();
            mouseX = event.clientX - rect.left;
            mouseY = event.clientY - rect.top;
        };

        window.addEventListener('resize', handleResize);
        canvas.addEventListener('mousemove', handleMouseMove);
        canvas.addEventListener('mouseleave', handleMouseLeave);
        canvas.addEventListener('mouseenter', handleMouseEnter);

        // Ajouter aussi des listeners sur le document pour une meilleure capture
        document.addEventListener('mousemove', handleMouseMove);

        return () => {
            cancelAnimationFrame(animationId);
            window.removeEventListener('resize', handleResize);
            canvas.removeEventListener('mousemove', handleMouseMove);
            canvas.removeEventListener('mouseleave', handleMouseLeave);
            canvas.removeEventListener('mouseenter', handleMouseEnter);
            document.removeEventListener('mousemove', handleMouseMove);
        };
    }, []);

    return (
        <div className="fixed top-0 left-0 w-full h-full -z-10" style={{ background: '#000' }}>
            <canvas
                ref={canvasRef}
                className="absolute top-0 left-0 w-full h-full"
                style={{
                    pointerEvents: 'auto',
                    zIndex: 1
                }}
            />
        </div>
    );
};

export default ConstellationBackground;
